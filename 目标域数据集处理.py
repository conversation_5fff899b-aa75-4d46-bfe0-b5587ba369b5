import os
import numpy as np
import pandas as pd
import scipy.io as sio
from matplotlib import pyplot as plt
from scipy import signal
from scipy.fft import fft, fftfreq
from scipy.signal import welch, hilbert, resample_poly
import pywt
from sklearn.preprocessing import StandardScaler
import pickle
import warnings
import scipy.stats

warnings.filterwarnings('ignore')
plt.rcParams['font.sans-serif'] = ['PingFang SC', 'SimHei', 'STHeiti', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False


class TargetDomainProcessor:
    def __init__(self, target_data_path, source_processing_params_path='processed_origin_data_result'):
        self.target_data_path = target_data_path
        self.source_processing_params_path = source_processing_params_path

        self.bearing_params = {
            'SKF6205': {'n': 9, 'd': 0.3126, 'D': 1.537},
            'SKF6203': {'n': 9, 'd': 0.2656, 'D': 1.122}
        }

        self.window_size = 4096
        self.overlap_ratio = 0.5
        self.target_fs = 32000

        self.target_files = []
        self.features_data = []

        self.load_source_processing_params()

    def load_source_processing_params(self):
        """加载源域的处理参数，确保一致性"""
        params_file = os.path.join(self.source_processing_params_path, 'processing_params.pickle')

        if os.path.exists(params_file):
            with open(params_file, 'rb') as f:
                params = pickle.load(f)

            self.window_size = params['window_size']
            self.overlap_ratio = params['overlap_ratio']
            self.target_fs = params['target_fs']

            print(f"已加载源域处理参数:")
            print(f"  窗口大小: {self.window_size}")
            print(f"  重叠率: {self.overlap_ratio}")
            print(f"  目标采样率: {self.target_fs} Hz")
        else:
            print(f"警告: 未找到源域处理参数文件 {params_file}")
            print(f"使用默认参数")

    def get_fault_frequencies(self, rpm, bearing_type='SKF6205'):
        """计算轴承故障特征频率"""
        params = self.bearing_params[bearing_type]
        fr = rpm / 60
        bpfo = fr * (params['n'] / 2) * (1 - params['d'] / params['D'])
        bpfi = fr * (params['n'] / 2) * (1 + params['d'] / params['D'])
        bsf = fr * (params['D'] / params['d']) * (1 - (params['d'] / params['D']) ** 2)
        return {'BPFO': bpfo, 'BPFI': bpfi, 'BSF': bsf, 'FR': fr}

    def resample_signal(self, signal_data, original_fs, target_fs):
        if original_fs == target_fs:
            return signal_data

        # 计算重采样比例
        if original_fs > target_fs:
            # 降采样
            up = target_fs
            down = original_fs
            # 简化分数
            gcd = np.gcd(up, down)
            up = up // gcd
            down = down // gcd
        else:
            # 升采样
            up = target_fs
            down = original_fs
            gcd = np.gcd(up, down)
            up = up // gcd
            down = down // gcd

        # 使用scipy的resample_poly进行重采样
        resampled = resample_poly(signal_data, up, down)
        return resampled

    def scan_target_files(self):
        """扫描目标域文件（A.mat到P.mat）"""
        target_files = []

        # A到P的文件列表
        file_names = [f"{chr(65 + i)}.mat" for i in range(16)]  # A.mat到P.mat

        print("=" * 60)
        print("扫描目标域数据文件")
        print("=" * 60)

        for file_name in file_names:
            file_path = os.path.join(self.target_data_path, file_name)

            if os.path.exists(file_path):
                try:
                    # 尝试加载文件以验证有效性
                    mat_data = sio.loadmat(file_path)
                    var_name = file_name.split('.')[0]  # A, B, C, ..., P

                    if var_name in mat_data:
                        signal_data = mat_data[var_name].flatten()
                        file_info = {
                            'file_name': file_name,
                            'file_path': file_path,
                            'var_name': var_name,
                            'signal_length': len(signal_data),
                            'original_fs': 32000,  # 目标域采样率32kHz
                            'signal_range': [np.min(signal_data), np.max(signal_data)]
                        }
                        target_files.append(file_info)
                        print(
                            f"✓ {file_name}: 长度={len(signal_data)}, 范围=[{np.min(signal_data):.3f}, {np.max(signal_data):.3f}]")
                    else:
                        print(f"✗ {file_name}: 未找到变量 {var_name}")

                except Exception as e:
                    print(f"✗ {file_name}: 加载失败 - {e}")
            else:
                print(f"✗ {file_name}: 文件不存在")

        self.target_files = target_files
        print(f"\n找到 {len(target_files)} 个有效的目标域文件")
        return target_files

    def extract_samples_from_signal(self, signal_data, window_size=None, overlap_ratio=None):
        """将长信号分割成多个样本窗口（与源域一致）"""
        if window_size is None:
            window_size = self.window_size
        if overlap_ratio is None:
            overlap_ratio = self.overlap_ratio

        step_size = int(window_size * (1 - overlap_ratio))
        samples = []

        for start in range(0, len(signal_data) - window_size + 1, step_size):
            window = signal_data[start:start + window_size]
            samples.append(window)

        return samples

    def check_signal_quality(self, signal_data):
        """检查信号质量（与源域一致）"""
        if np.any(np.isnan(signal_data)) or np.any(np.isinf(signal_data)):
            return False

        signal_std = np.std(signal_data)
        if signal_std < 1e-8:
            return False

        signal_max = np.max(np.abs(signal_data))
        if signal_max > 100 * signal_std:
            return False

        return True

    def extract_time_domain_features(self, signal_data):
        """提取时域特征（与源域完全一致）"""
        features = {}

        features['mean'] = np.mean(signal_data)
        features['std'] = np.std(signal_data)
        features['var'] = np.var(signal_data)
        features['rms'] = np.sqrt(np.mean(signal_data ** 2))
        features['peak'] = np.max(np.abs(signal_data))
        features['peak_to_peak'] = np.ptp(signal_data)

        features['skewness'] = scipy.stats.skew(signal_data)
        features['kurtosis'] = scipy.stats.kurtosis(signal_data)

        mean_abs = np.mean(np.abs(signal_data))
        if mean_abs > 0:
            features['crest_factor'] = features['peak'] / features['rms'] if features['rms'] > 0 else 0
            features['impulse_factor'] = features['peak'] / mean_abs
            features['shape_factor'] = features['rms'] / mean_abs if mean_abs > 0 else 0
            sqrt_mean_sqrt = np.mean(np.sqrt(np.abs(signal_data)))
            features['clearance_factor'] = features['peak'] / (sqrt_mean_sqrt ** 2) if sqrt_mean_sqrt > 0 else 0
        else:
            features['crest_factor'] = 0
            features['impulse_factor'] = 0
            features['shape_factor'] = 0
            features['clearance_factor'] = 0

        return features

    def extract_frequency_domain_features(self, signal_data, fs, rpm):
        """提取频域特征（与源域完全一致）"""
        features = {}

        N = len(signal_data)
        fft_vals = fft(signal_data)
        freqs = fftfreq(N, 1 / fs)[:N // 2]
        magnitude = np.abs(fft_vals[:N // 2])

        total_magnitude = np.sum(magnitude)
        if total_magnitude == 0:
            return {
                'spectral_centroid': 0, 'spectral_variance': 0, 'spectral_skewness': 0, 'spectral_kurtosis': 0,
                'BPFO_amplitude': 0, 'BPFI_amplitude': 0, 'BSF_amplitude': 0, 'FR_amplitude': 0,
                'low_freq_energy_ratio': 0, 'mid_freq_energy_ratio': 0, 'high_freq_energy_ratio': 0
            }

        features['spectral_centroid'] = np.sum(freqs * magnitude) / total_magnitude
        spectral_variance = np.sum(((freqs - features['spectral_centroid']) ** 2) * magnitude) / total_magnitude
        features['spectral_variance'] = spectral_variance

        if spectral_variance > 0:
            features['spectral_skewness'] = np.sum(((freqs - features['spectral_centroid']) ** 3) * magnitude) / (
                    total_magnitude * spectral_variance ** 1.5)
            features['spectral_kurtosis'] = np.sum(((freqs - features['spectral_centroid']) ** 4) * magnitude) / (
                    total_magnitude * spectral_variance ** 2)
        else:
            features['spectral_skewness'] = 0
            features['spectral_kurtosis'] = 0

        try:
            fault_freqs = self.get_fault_frequencies(rpm)

            freq_resolution = fs / N

            for fault_type, freq in fault_freqs.items():
                if freq > 0 and freq < fs / 2:  # 确保频率在有效范围内
                    # 找到最接近的频率索引
                    freq_idx = np.argmin(np.abs(freqs - freq))

                    freq_range_hz = max(5.0, freq * 0.05)
                    freq_range_points = max(10, int(freq_range_hz / freq_resolution))

                    start_idx = max(0, freq_idx - freq_range_points // 2)
                    end_idx = min(len(magnitude), freq_idx + freq_range_points // 2 + 1)

                    if end_idx > start_idx:

                        search_magnitudes = magnitude[start_idx:end_idx]
                        max_amplitude = np.max(search_magnitudes)
                        mean_amplitude = np.mean(search_magnitudes)
                        features[f'{fault_type}_amplitude'] = max_amplitude

                    else:
                        features[f'{fault_type}_amplitude'] = 0

                else:

                    features[f'{fault_type}_amplitude'] = 0

        except Exception as e:
            print(f"故障特征频率计算失败: {e}, RPM: {rpm}")
            features['BPFO_amplitude'] = 0
            features['BPFI_amplitude'] = 0
            features['BSF_amplitude'] = 0
            features['FR_amplitude'] = 0


        total_energy = np.sum(magnitude ** 2)
        if total_energy > 0:
            # 低频段 (0-500Hz)
            low_freq_idx = np.where(freqs <= 500)[0]
            features['low_freq_energy_ratio'] = np.sum(magnitude[low_freq_idx] ** 2) / total_energy if len(
                low_freq_idx) > 0 else 0

            # 中频段 (500-5000Hz)
            mid_freq_idx = np.where((freqs > 500) & (freqs <= 5000))[0]
            features['mid_freq_energy_ratio'] = np.sum(magnitude[mid_freq_idx] ** 2) / total_energy if len(
                mid_freq_idx) > 0 else 0

            # 高频段 (5000Hz以上)
            high_freq_idx = np.where(freqs > 5000)[0]
            features['high_freq_energy_ratio'] = np.sum(magnitude[high_freq_idx] ** 2) / total_energy if len(
                high_freq_idx) > 0 else 0
        else:
            features['low_freq_energy_ratio'] = 0
            features['mid_freq_energy_ratio'] = 0
            features['high_freq_energy_ratio'] = 0

        return features

    def extract_time_frequency_features(self, signal_data, fs):
        """提取时频域特征"""
        features = {}

        try:
            wavelet = 'db4'
            levels = 4
            wp = pywt.WaveletPacket(signal_data, wavelet, maxlevel=levels)

            energy_features = []
            for i in range(2 ** levels):
                try:
                    node_name = [node.path for node in wp.get_level(levels, 'freq')][i]
                    coeffs = wp[node_name].data
                    energy = np.sum(coeffs ** 2)
                    energy_features.append(energy)
                except:
                    energy_features.append(0)

            total_energy = sum(energy_features)
            if total_energy > 0:
                for i, energy in enumerate(energy_features):
                    features[f'wavelet_energy_band_{i}'] = energy / total_energy

                energy_ratios = np.array(energy_features) / total_energy
                energy_ratios = energy_ratios[energy_ratios > 0]
                if len(energy_ratios) > 0:
                    features['wavelet_entropy'] = -np.sum(energy_ratios * np.log2(energy_ratios + 1e-12))
                else:
                    features['wavelet_entropy'] = 0
            else:
                for i in range(2 ** levels):
                    features[f'wavelet_energy_band_{i}'] = 0
                features['wavelet_entropy'] = 0

            analytic_signal = hilbert(signal_data)
            envelope = np.abs(analytic_signal)

            features['envelope_mean'] = np.mean(envelope)
            features['envelope_std'] = np.std(envelope)
            features['envelope_skewness'] = scipy.stats.skew(envelope)
            features['envelope_kurtosis'] = scipy.stats.kurtosis(envelope)

        except Exception as e:
            print(f"时频特征提取失败: {e}")
            for i in range(16):
                features[f'wavelet_energy_band_{i}'] = 0
            features['wavelet_entropy'] = 0
            features['envelope_mean'] = 0
            features['envelope_std'] = 0
            features['envelope_skewness'] = 0
            features['envelope_kurtosis'] = 0

        return features

    def extract_features(self, signal_data, sensor_type, fs, rpm, file_info):
        """提取完整特征集（与源域完全一致）"""
        features = {}

        # 添加文件信息
        features['file_path'] = file_info['file_path']
        features['fault_type'] = file_info.get('fault_type', 'Unknown')
        features['sensor_type'] = sensor_type
        features['rpm'] = rpm
        features['original_fs'] = file_info['original_fs']
        features['resampled_fs'] = fs

        signal_data = signal_data - np.mean(signal_data)

        # 时域特征
        time_features = self.extract_time_domain_features(signal_data)
        for key, value in time_features.items():
            features[f'{sensor_type}_{key}'] = value

        # 频域特征
        freq_features = self.extract_frequency_domain_features(signal_data, fs, rpm)
        for key, value in freq_features.items():
            features[f'{sensor_type}_{key}'] = value

        # 时频域特征
        time_freq_features = self.extract_time_frequency_features(signal_data, fs)
        for key, value in time_freq_features.items():
            features[f'{sensor_type}_{key}'] = value

        return features

    def process_all_target_files(self, rpm=1797):
        """处理所有目标域文件"""
        print("\n" + "=" * 60)
        print("开始目标域特征提取")
        print("=" * 60)
        print(f"目标采样率: {self.target_fs} Hz")
        print(f"窗口参数: 窗口大小={self.window_size}, 重叠率={self.overlap_ratio}")
        print(f"默认RPM: {rpm}")

        all_features = []
        total_samples = 0

        for file_info in self.target_files:
            file_name = file_info['file_name']
            file_path = file_info['file_path']
            var_name = file_info['var_name']
            original_fs = file_info['original_fs']

            print(f"\n处理文件: {file_name}")
            print(f"  原始采样率: {original_fs} Hz")

            try:
                mat_data = sio.loadmat(file_path)
                signal_data = mat_data[var_name].flatten()

                signal_length = len(signal_data)
                print(f"  原始信号长度: {signal_length} 点 ({signal_length / original_fs:.2f} 秒)")

                if original_fs != self.target_fs:
                    resampled_signal = self.resample_signal(signal_data, original_fs, self.target_fs)
                    print(
                        f"  重采样后长度: {len(resampled_signal)} 点 ({len(resampled_signal) / self.target_fs:.2f} 秒)")
                else:
                    resampled_signal = signal_data
                    print(f"  无需重采样")

                samples = self.extract_samples_from_signal(resampled_signal)
                valid_samples = 0

                for i, sample in enumerate(samples):
                    # 检查样本质量
                    if not self.check_signal_quality(sample):
                        continue

                    sample_file_info = {
                        'file_path': f"{file_name}_sample_{i}",
                        'file_name': file_name,
                        'sample_index': i,
                        'original_fs': original_fs
                    }

                    features = self.extract_features(sample, 'DE', self.target_fs, rpm, sample_file_info)

                    features['source_file'] = file_name
                    features['sample_index'] = i

                    all_features.append(features)
                    valid_samples += 1

                print(f"  提取了 {valid_samples} 个有效样本")
                total_samples += valid_samples

            except Exception as e:
                print(f"  处理失败: {e}")
                continue

        self.features_data = all_features
        print(f"\n目标域特征提取完成:")
        print(f"  总文件数: {len(self.target_files)}")
        print(f"  总样本数: {len(all_features)}")

        if all_features:
            df_temp = pd.DataFrame(all_features)
            print(f"  每个文件平均样本数: {len(all_features) / len(self.target_files):.1f}")

            file_sample_counts = df_temp['source_file'].value_counts().sort_index()
            print(f"\n各文件样本数:")
            for file_name, count in file_sample_counts.items():
                print(f"    {file_name}: {count}")

        return all_features

    def save_processed_data(self, output_dir='processed_target_data_result'):
        """保存处理后的目标域数据"""
        os.makedirs(output_dir, exist_ok=True)

        with open(os.path.join(output_dir, 'target_files.pickle'), 'wb') as f:
            pickle.dump(self.target_files, f)

        processing_params = {
            'window_size': self.window_size,
            'overlap_ratio': self.overlap_ratio,
            'target_fs': self.target_fs,
            'original_fs': 32000,
            'source_processing_params_path': self.source_processing_params_path
        }
        with open(os.path.join(output_dir, 'processing_params.pickle'), 'wb') as f:
            pickle.dump(processing_params, f)

        if self.features_data:
            df = pd.DataFrame(self.features_data)

            df.to_csv(os.path.join(output_dir, 'target_features.csv'), index=False)

            df.to_pickle(os.path.join(output_dir, 'target_features.pkl'))

            feature_stats = df.describe()
            feature_stats.to_csv(os.path.join(output_dir, 'target_feature_statistics.csv'))

            for file_name in df['source_file'].unique():
                file_data = df[df['source_file'] == file_name]
                if not file_data.empty:
                    safe_file_name = file_name.replace('.mat', '')
                    file_data.to_csv(os.path.join(output_dir, f'target_features_{safe_file_name}.csv'), index=False)

            print(f"\n目标域数据保存完成，输出目录: {output_dir}")
            print(f"  - 特征数据: target_features.csv/pkl")
            print(f"  - 文件列表: target_files.pickle")
            print(f"  - 处理参数: processing_params.pickle")
            print(f"  - 统计信息: target_feature_statistics.csv")
            print(f"  - 分文件数据: target_features_A.csv, target_features_B.csv, ...")

            print(f"\n目标域数据集摘要:")
            print(f"  总样本数: {len(df)}")
            print(f"  特征维度: {len(df.columns)}")
            print(f"  处理的文件数: {len(df['source_file'].unique())}")

            print("  各文件样本数:")
            file_counts = df['source_file'].value_counts().sort_index()
            for file_name, count in file_counts.items():
                print(f"    {file_name}: {count}")

        return output_dir

    def compare_with_source_features(self, source_features_path):
        """与源域特征进行对比分析"""
        if not os.path.exists(source_features_path):
            print(f"源域特征文件不存在: {source_features_path}")
            return

        print("\n" + "=" * 60)
        print("源域与目标域特征对比分析")
        print("=" * 60)

        source_df = pd.read_csv(source_features_path)
        target_df = pd.DataFrame(self.features_data)

        exclude_cols = ['file_path', 'fault_type', 'sensor_type', 'rpm', 'original_fs',
                        'resampled_fs', 'source_file', 'sample_index', 'file_name']

        source_feature_cols = [col for col in source_df.columns if col not in exclude_cols]
        target_feature_cols = [col for col in target_df.columns if col not in exclude_cols]

        print(f"源域特征数: {len(source_feature_cols)}")
        print(f"目标域特征数: {len(target_feature_cols)}")

        common_features = set(source_feature_cols) & set(target_feature_cols)
        source_only = set(source_feature_cols) - set(target_feature_cols)
        target_only = set(target_feature_cols) - set(source_feature_cols)

        print(f"共同特征数: {len(common_features)}")
        if source_only:
            print(f"仅源域特征: {len(source_only)} - {list(source_only)[:5]}")
        if target_only:
            print(f"仅目标域特征: {len(target_only)} - {list(target_only)[:5]}")

        if common_features:
            common_features_list = sorted(list(common_features))
            source_stats = source_df[common_features_list].describe()
            target_stats = target_df[common_features_list].describe()

            for feature in common_features_list[:10]:
                s_mean = source_stats.loc['mean', feature]
                t_mean = target_stats.loc['mean', feature]
                s_std = source_stats.loc['std', feature]
                t_std = target_stats.loc['std', feature]

                print(f"{feature:<25} {s_mean:<12.4f} {t_mean:<12.4f} {s_std:<12.4f} {t_std:<12.4f}")

        return {
            'source_feature_count': len(source_feature_cols),
            'target_feature_count': len(target_feature_cols),
            'common_features': len(common_features),
            'source_only_features': len(source_only),
            'target_only_features': len(target_only)
        }


def main():
    """主函数"""
    print("=" * 80)
    print("目标域数据处理器 ")
    print("=" * 80)

    target_data_path = "目标域数据集"
    source_processing_params_path = "processed_origin_data_result"  # 源域处理参数路径

    # 创建处理器
    processor = TargetDomainProcessor(target_data_path, source_processing_params_path)

    # 步骤1: 扫描目标域文件
    print("步骤1: 扫描目标域文件...")
    target_files = processor.scan_target_files()

    if not target_files:
        print("未找到有效的目标域文件，请检查路径和文件名")
        return

    # 步骤2: 处理所有目标域文件
    print("\n步骤2: 特征提取...")
    processor.process_all_target_files(rpm=1797)  # 使用默认RPM

    # 步骤3: 保存处理后的数据
    print("\n步骤3: 保存数据...")
    output_dir = processor.save_processed_data()

    # 步骤4: 与源域特征对比分析
    print("\n步骤4: 与源域特征对比...")
    source_features_file = os.path.join(source_processing_params_path, 'extracted_features.csv')
    comparison_result = processor.compare_with_source_features(source_features_file)

    print(f"\n目标域数据处理完成！")
    print(f"输出目录: {output_dir}")
    print(f"特征一致性: {comparison_result['common_features']}/{comparison_result['source_feature_count']} 特征匹配")

    # 创建迁移学习兼容的数据格式
    print("\n步骤5: 创建迁移学习兼容格式...")
    create_transfer_compatible_format(output_dir, source_processing_params_path)


def create_transfer_compatible_format(target_output_dir, source_dir):
    """创建与迁移学习代码兼容的数据格式"""
    print("创建迁移学习兼容格式...")

    try:
        # 加载目标域特征
        target_df = pd.read_csv(os.path.join(target_output_dir, 'target_features.csv'))

        # 加载源域特征结构
        source_df = pd.read_csv(os.path.join(source_dir, 'extracted_features.csv'))

        # 获取源域特征列
        exclude_cols = ['file_path', 'fault_type', 'sensor_type', 'rpm', 'original_fs', 'resampled_fs']
        source_feature_cols = [col for col in source_df.columns if col not in exclude_cols]

        # 确保目标域具有相同的特征列
        target_feature_cols = [col for col in target_df.columns if
                               col not in exclude_cols + ['source_file', 'sample_index', 'file_name']]

        # 创建兼容的特征矩阵
        compatible_features = []
        missing_features = []

        for col in source_feature_cols:
            if col in target_feature_cols:
                compatible_features.append(col)
            else:
                missing_features.append(col)

        if missing_features:
            print(f"警告: 缺少 {len(missing_features)} 个源域特征")
            print(f"缺少的特征: {missing_features[:5]}...")

            # 用零填充缺少的特征
            for col in missing_features:
                target_df[col] = 0.0

        # 按源域特征顺序重新排列
        ordered_target_df = target_df[['source_file'] + source_feature_cols].copy()

        # 保存兼容格式
        compatible_file = os.path.join(target_output_dir, 'target_features_compatible.csv')
        ordered_target_df.to_csv(compatible_file, index=False)

        # 创建文件映射
        file_mapping = {}
        for idx, row in target_df.iterrows():
            source_file = row['source_file']
            if source_file not in file_mapping:
                file_mapping[source_file] = []
            file_mapping[source_file].append(idx)

        # 保存文件映射
        with open(os.path.join(target_output_dir, 'file_mapping.pickle'), 'wb') as f:
            pickle.dump(file_mapping, f)

        print(f"兼容格式创建完成:")
        print(f"  - 兼容特征文件: target_features_compatible.csv")
        print(f"  - 特征维度: {len(source_feature_cols)}")
        print(f"  - 样本数: {len(ordered_target_df)}")
        print(f"  - 匹配特征数: {len(compatible_features)}")
        print(f"  - 填充特征数: {len(missing_features)}")

        return compatible_file

    except Exception as e:
        print(f"创建兼容格式失败: {e}")
        return None


if __name__ == "__main__":
    main()