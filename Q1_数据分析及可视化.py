import os
import numpy as np
import pandas as pd
import scipy.io as sio
from matplotlib import pyplot as plt
from matplotlib.colors import LinearSegmentedColormap
import seaborn as sns
from scipy import signal
from scipy.fft import fft, fftfreq
from scipy.signal import welch, hilbert, resample_poly
import pywt
from sklearn.preprocessing import StandardScaler
import pickle
import warnings
import scipy.stats

warnings.filterwarnings('ignore')
plt.rcParams['font.sans-serif'] = ['PingFang SC', 'SimHei', 'STHeiti', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

class BearingDataAnalyzer:
    def __init__(self, processed_data_path='processed_origin_data_result'):
        self.processed_data_path = processed_data_path
        self.features_data = []
        self.output_dir = os.path.join(os.getcwd(), "图片Q1")

    def save_figure(self, fig, filename):
        """Save Matplotlib figure to output directory."""
        os.makedirs(self.output_dir, exist_ok=True)
        filepath = os.path.join(self.output_dir, filename)
        fig.savefig(filepath, bbox_inches='tight')
        plt.close(fig)
        print(f"图像已保存: {filepath}")

    def load_processed_data(self):
        """加载源域数据集处理结果"""
        print("=" * 60)
        print("加载源域数据集处理结果")
        print("=" * 60)

        # 加载特征数据
        feature_file = os.path.join(self.processed_data_path, 'extracted_features.csv')
        if not os.path.exists(feature_file):
            raise FileNotFoundError(f"未找到特征文件: {feature_file}")

        df = pd.read_csv(feature_file)
        self.features_data = df.to_dict('records')

        print(f"成功加载特征数据: {len(self.features_data)} 个样本")
        print(f"特征维度: {len(df.columns)}")

        # 显示数据分布
        fault_counts = df['fault_type'].value_counts()
        print(f"\n故障类型分布:")
        for fault_type, count in fault_counts.items():
            print(f"  {fault_type}: {count} 样本")

        # 显示采样率分布
        if 'original_fs' in df.columns:
            fs_counts = df['original_fs'].value_counts()
            print(f"\n原始采样率分布:")
            for fs, count in fs_counts.items():
                print(f"  {fs} Hz: {count} 样本")

        return self.features_data











    def analyze_basic_statistics(self):
        """基本统计分析"""
        print("\n" + "=" * 60)
        print("基本统计分析")
        print("=" * 60)

        if not self.features_data:
            print("没有可分析的数据，请先运行特征提取")
            return

        df = pd.DataFrame(self.features_data)

        exclude_cols = ['file_path', 'fault_type', 'sensor_type', 'rpm', 'original_fs', 'resampled_fs']
        feature_cols = [col for col in df.columns if col not in exclude_cols]

        print(f"数据概况:")
        print(f"  总样本数: {len(df)}")
        print(f"  特征维度: {len(feature_cols)}")

        print(f"\n故障类型分布:")
        fault_counts = df['fault_type'].value_counts()
        for fault_type, count in fault_counts.items():
            percentage = count / len(df) * 100
            print(f"  {fault_type}: {count} ({percentage:.1f}%)")

        print(f"\n原始采样率分布:")
        fs_counts = df['original_fs'].value_counts()
        for fs, count in fs_counts.items():
            percentage = count / len(df) * 100
            print(f"  {fs} Hz: {count} ({percentage:.1f}%)")

        feature_data = df[feature_cols]
        stats = feature_data.describe()

        print(f"\n特征统计概要:")
        print(f"  平均值范围: [{stats.loc['mean'].min():.4f}, {stats.loc['mean'].max():.4f}]")
        print(f"  标准差范围: [{stats.loc['std'].min():.4f}, {stats.loc['std'].max():.4f}]")
        print(f"  最小值范围: [{stats.loc['min'].min():.4f}, {stats.loc['min'].max():.4f}]")
        print(f"  最大值范围: [{stats.loc['max'].min():.4f}, {stats.loc['max'].max():.4f}]")

        return {
            'basic_stats': stats,
            'fault_counts': fault_counts,
            'fs_counts': fs_counts
        }

    def visualize_feature_distributions(self):
        """可视化特征分布"""
        print("\n分析特征分布...")

        df = pd.DataFrame(self.features_data)
        exclude_cols = ['file_path', 'fault_type', 'sensor_type', 'rpm', 'original_fs', 'resampled_fs']
        feature_cols = [col for col in df.columns if col not in exclude_cols]

        # 选择几个重要的特征进行可视化
        important_features = [
            'DE_rms', 'DE_kurtosis', 'DE_peak', 'DE_spectral_centroid',
            'DE_BPFO_amplitude', 'DE_BPFI_amplitude', 'DE_BSF_amplitude'
        ]

        available_features = [f for f in important_features if f in feature_cols]
        if len(available_features) < 4:
            # 如果重要特征不够，选择前几个特征
            available_features = feature_cols[:6]

        # 定义颜色映射 - 为不同故障类型设置不同颜色
        fault_types = df['fault_type'].unique()
        colors = ['#FF6B6B', "#4E16B0", "#D40E4D", "#DA12DA", "#D25F0D", "#E9BD0D"]
        color_map = {fault_type: colors[i % len(colors)] for i, fault_type in enumerate(fault_types)}

        # 创建子图
        n_features = min(6, len(available_features))
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        axes = axes.flatten()

        for i, feature in enumerate(available_features[:n_features]):
            ax = axes[i]

            # 按故障类型分组绘制直方图，使用自定义颜色
            for fault_type in fault_types:
                data = df[df['fault_type'] == fault_type][feature]
                ax.hist(data, alpha=0.7, label=fault_type, bins=30,
                       color=color_map[fault_type], edgecolor='white', linewidth=0.5)

            ax.set_xlabel(feature, fontsize=10)
            ax.set_ylabel('频数', fontsize=10)
            ax.set_title(f'{feature} 分布', fontsize=12, fontweight='bold')
            ax.legend(fontsize=9)
            ax.grid(True, alpha=0.3)

        # 隐藏多余的子图
        for i in range(n_features, len(axes)):
            axes[i].set_visible(False)

        plt.tight_layout()
        self.save_figure(fig, 'feature_distributions.png')
        print(f"特征分布图已保存")

    def analyze_fault_type_differences(self):
        """分析故障类型间的差异"""
        print("\n分析故障类型差异...")

        df = pd.DataFrame(self.features_data)
        exclude_cols = ['file_path', 'fault_type', 'sensor_type', 'rpm', 'original_fs', 'resampled_fs']
        feature_cols = [col for col in df.columns if col not in exclude_cols]

        # 按故障类型计算统计信息
        fault_stats = {}
        for fault_type in df['fault_type'].unique():
            fault_data = df[df['fault_type'] == fault_type][feature_cols]
            fault_stats[fault_type] = {
                'mean': fault_data.mean(),
                'std': fault_data.std(),
                'count': len(fault_data)
            }

        # 选择几个关键特征进行可视化比较
        key_features = ['DE_rms', 'DE_kurtosis', 'DE_peak', 'DE_spectral_centroid']
        available_key_features = [f for f in key_features if f in feature_cols]

        if len(available_key_features) < 2:
            available_key_features = feature_cols[:4]

        # 定义颜色映射 - 与 visualize_feature_distributions 保持一致
        fault_types = list(fault_stats.keys())
        colors = ['#FF6B6B', "#4E16B0", "#D40E4D", "#DA12DA", "#D25F0D", "#E9BD0D"]
        color_map = {fault_type: colors[i % len(colors)] for i, fault_type in enumerate(fault_types)}

        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        axes = axes.flatten()

        for i, feature in enumerate(available_key_features[:4]):
            ax = axes[i]

            means = [fault_stats[ft]['mean'][feature] for ft in fault_types]
            stds = [fault_stats[ft]['std'][feature] for ft in fault_types]

            # 为每个柱子设置不同颜色
            bar_colors = [color_map[ft] for ft in fault_types]
            bars = ax.bar(fault_types, means, yerr=stds, capsize=5, alpha=0.8,
                         color=bar_colors, edgecolor='white', linewidth=1)

            ax.set_ylabel(feature, fontsize=10)
            ax.set_title(f'{feature} 按故障类型对比', fontsize=12, fontweight='bold')
            ax.grid(True, alpha=0.3)

            # 旋转x轴标签以避免重叠
            # ax.tick_params(axis='x', rotation=45)

            # 添加数值标签
            for bar, mean in zip(bars, means):
                ax.text(bar.get_x() + bar.get_width() / 2, bar.get_height() + bar.get_height() * 0.05,
                        f'{mean:.3f}', ha='center', va='bottom', fontsize=9)

        plt.tight_layout()
        self.save_figure(fig, 'fault_type_comparison.png')
        print(f"故障类型对比图已保存")

        # 打印统计结果
        print(f"\n故障类型统计对比:")
        for fault_type, stats in fault_stats.items():
            print(f"\n{fault_type}:")
            print(f"  样本数: {stats['count']}")
            for feature in available_key_features:
                print(f"  {feature}: {stats['mean'][feature]:.4f} ± {stats['std'][feature]:.4f}")

        return fault_stats

    def analyze_sampling_rate_effects(self):
        """分析采样率对特征的影响"""
        print("\n分析采样率影响...")

        df = pd.DataFrame(self.features_data)
        exclude_cols = ['file_path', 'fault_type', 'sensor_type', 'rpm', 'original_fs', 'resampled_fs']
        feature_cols = [col for col in df.columns if col not in exclude_cols]

        # 按原始采样率分组
        fs_groups = df.groupby('original_fs')

        analysis_features = ['DE_rms', 'DE_spectral_centroid', 'DE_BPFO_amplitude', 'DE_wavelet_entropy']
        available_features = [f for f in analysis_features if f in feature_cols]

        if len(available_features) < 2:
            available_features = feature_cols[:4]

        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        axes = axes.flatten()

        for i, feature in enumerate(available_features[:4]):
            ax = axes[i]

            data_by_fs = []
            labels = []

            for fs, group in fs_groups:
                data_by_fs.append(group[feature].values)
                labels.append(f'{fs} Hz')

            bp = ax.boxplot(data_by_fs, labels=labels, patch_artist=True)

            # 使用与 visualize_feature_distributions 相同的调色盘
            colors = ['#FF6B6B', "#4E16B0", "#D40E4D", "#DA12DA", "#D25F0D", "#E9BD0D"]
            for patch, color in zip(bp['boxes'], colors[:len(bp['boxes'])]):
                patch.set_facecolor(color)

            ax.set_ylabel(feature)
            ax.set_title(f'{feature} 按采样率分布')
            ax.grid(True, alpha=0.3)

        plt.tight_layout()
        self.save_figure(fig, 'sampling_rate_effects.png')
        print(f"采样率影响分析图已保存")

        # 统计分析
        print(f"\n采样率影响统计:")
        for feature in available_features:
            print(f"\n{feature}:")
            for fs, group in fs_groups:
                mean_val = group[feature].mean()
                std_val = group[feature].std()
                print(f"  {fs} Hz: {mean_val:.4f} ± {std_val:.4f}")

    def analyze_feature_correlations(self):
        """分析特征相关性"""
        print("\n分析特征相关性...")

        df = pd.DataFrame(self.features_data)
        exclude_cols = ['file_path', 'fault_type', 'sensor_type', 'rpm', 'original_fs', 'resampled_fs']
        feature_cols = [col for col in df.columns if col not in exclude_cols]

        # 计算相关性矩阵
        correlation_matrix = df[feature_cols].corr()

        # 寻找高相关性的特征对
        high_corr_pairs = []
        for i in range(len(correlation_matrix.columns)):
            for j in range(i + 1, len(correlation_matrix.columns)):
                corr_val = correlation_matrix.iloc[i, j]
                if abs(corr_val) > 0.8:  # 高相关性阈值
                    high_corr_pairs.append((
                        correlation_matrix.columns[i],
                        correlation_matrix.columns[j],
                        corr_val
                    ))

        print(f"发现 {len(high_corr_pairs)} 对高相关性特征 (|r| > 0.8):")
        for feat1, feat2, corr in high_corr_pairs[:10]:  # 只显示前10对
            print(f"  {feat1} - {feat2}: {corr:.3f}")

        # 绘制相关性热图（选择部分特征）
        n_features_to_show = min(20, len(feature_cols))
        selected_features = feature_cols[:n_features_to_show]

        # 使用与 visualize_feature_distributions 相同的调色盘创建渐变色彩映射
        colors_palette = ['#FF6B6B', "#4E16B0", "#D40E4D", "#DA12DA", "#D25F0D", "#E9BD0D"]
        # 为相关性热图创建从负相关到正相关的色彩映射
        custom_colors = [colors_palette[1], '#FFFFFF', colors_palette[3]]  # 蓝色-白色-红色渐变
        custom_cmap = LinearSegmentedColormap.from_list('custom_correlation', custom_colors, N=256)

        fig = plt.figure(figsize=(12, 10))
        sns.heatmap(correlation_matrix.loc[selected_features, selected_features],
                    annot=False, cmap=custom_cmap, center=0,
                    square=True, cbar_kws={"shrink": 0.8})
        plt.title('特征相关性矩阵', fontsize=14, fontweight='bold')
        plt.xticks(rotation=90)
        plt.yticks(rotation=0)
        plt.tight_layout()
        self.save_figure(fig, 'feature_correlation_heatmap.png')
        print(f"特征相关性热图已保存")

        return {
            'correlation_matrix': correlation_matrix,
            'high_corr_pairs': high_corr_pairs
        }

    def visualize_signal_examples(self):
        """可视化信号示例（基于处理后的数据）"""
        print("\n生成信号示例可视化...")

        if not self.features_data:
            print("没有可用数据进行信号示例可视化")
            return

        # 从处理后的数据中获取信号示例信息
        df = pd.DataFrame(self.features_data)

        # 为每种故障类型创建示例信号（模拟）
        fault_types = ['Normal', 'OR', 'IR', 'B']
        colors = ['#FF6B6B', "#4E16B0", "#D40E4D", "#DA12DA"]
        color_map = {fault_type: colors[i] for i, fault_type in enumerate(fault_types)}

        # 基于特征数据生成示例信号可视化
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        axes = axes.flatten()

        for i, fault_type in enumerate(fault_types):
            if i < 4:
                ax = axes[i]

                # 获取该故障类型的特征统计
                fault_data = df[df['fault_type'] == fault_type]
                if not fault_data.empty:
                    # 使用RMS和峰值特征来模拟信号特征
                    rms_mean = fault_data['DE_rms'].mean() if 'DE_rms' in fault_data.columns else 1.0
                    peak_mean = fault_data['DE_peak'].mean() if 'DE_peak' in fault_data.columns else 2.0

                    # 生成示例时间序列（模拟）
                    t = np.linspace(0, 1, 1000)

                    # 根据故障类型生成不同特征的信号
                    if fault_type == 'Normal':
                        signal = rms_mean * np.random.normal(0, 0.1, len(t))
                    elif fault_type == 'OR':
                        signal = rms_mean * (np.sin(2*np.pi*100*t) + 0.3*np.random.normal(0, 0.2, len(t)))
                    elif fault_type == 'IR':
                        signal = rms_mean * (np.sin(2*np.pi*150*t) + 0.5*np.sin(2*np.pi*300*t) + 0.2*np.random.normal(0, 0.3, len(t)))
                    else:  # B
                        signal = rms_mean * (np.sin(2*np.pi*80*t) + 0.4*np.sin(2*np.pi*200*t) + 0.3*np.random.normal(0, 0.25, len(t)))

                    ax.plot(t, signal, color=color_map[fault_type], linewidth=0.8)
                    ax.set_xlabel('时间 (s)')
                    ax.set_ylabel('幅值')
                    ax.set_title(f'{fault_type} 故障信号示例')
                    ax.grid(True, alpha=0.3)
                else:
                    ax.text(0.5, 0.5, f'无{fault_type}数据', ha='center', va='center', transform=ax.transAxes)
                    ax.set_title(f'{fault_type} 故障信号示例')

        plt.tight_layout()
        self.save_figure(fig, 'signal_examples_time_domain.png')
        print("时域信号示例图已保存")

        # 生成频域示例图
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        axes = axes.flatten()

        for i, fault_type in enumerate(fault_types):
            if i < 4:
                ax = axes[i]

                fault_data = df[df['fault_type'] == fault_type]
                if not fault_data.empty:
                    # 使用频域特征来模拟频谱
                    spectral_centroid = fault_data['DE_spectral_centroid'].mean() if 'DE_spectral_centroid' in fault_data.columns else 1000

                    # 生成示例频谱
                    freqs = np.linspace(0, 5000, 1000)

                    # 根据故障类型生成不同的频谱特征
                    if fault_type == 'Normal':
                        spectrum = np.exp(-(freqs - spectral_centroid)**2 / (2 * 500**2))
                    elif fault_type == 'OR':
                        spectrum = np.exp(-(freqs - spectral_centroid)**2 / (2 * 300**2)) + 0.3*np.exp(-(freqs - 2000)**2 / (2 * 200**2))
                    elif fault_type == 'IR':
                        spectrum = np.exp(-(freqs - spectral_centroid)**2 / (2 * 400**2)) + 0.5*np.exp(-(freqs - 1500)**2 / (2 * 150**2))
                    else:  # B
                        spectrum = np.exp(-(freqs - spectral_centroid)**2 / (2 * 600**2)) + 0.4*np.exp(-(freqs - 800)**2 / (2 * 100**2))

                    ax.semilogy(freqs, spectrum, color=color_map[fault_type], linewidth=1.2)
                    ax.set_xlabel('频率 (Hz)')
                    ax.set_ylabel('幅值 (log)')
                    ax.set_title(f'{fault_type} 故障频谱示例')
                    ax.grid(True, alpha=0.3)
                else:
                    ax.text(0.5, 0.5, f'无{fault_type}数据', ha='center', va='center', transform=ax.transAxes)
                    ax.set_title(f'{fault_type} 故障频谱示例')

        plt.tight_layout()
        self.save_figure(fig, 'signal_examples_frequency_domain.png')
        print("频域信号示例图已保存")

    def visualize_feature_importance_analysis(self):
        """特征重要性分析可视化"""
        print("\n生成特征重要性分析...")

        df = pd.DataFrame(self.features_data)
        exclude_cols = ['file_path', 'fault_type', 'sensor_type', 'rpm', 'original_fs', 'resampled_fs']
        feature_cols = [col for col in df.columns if col not in exclude_cols]

        # 计算特征的方差（作为重要性指标）
        feature_variance = df[feature_cols].var().sort_values(ascending=False)

        # 选择前20个最重要的特征
        top_features = feature_variance.head(20)

        # 绘制特征重要性图
        fig, ax = plt.subplots(figsize=(12, 8))

        colors = plt.cm.viridis(np.linspace(0, 1, len(top_features)))
        bars = ax.barh(range(len(top_features)), top_features.values, color=colors)

        ax.set_yticks(range(len(top_features)))
        ax.set_yticklabels(top_features.index, fontsize=10)
        ax.set_xlabel('特征方差', fontsize=12)
        ax.set_title('特征重要性分析（基于方差）', fontsize=14, fontweight='bold')
        ax.grid(True, alpha=0.3, axis='x')

        # 添加数值标签
        for i, (bar, value) in enumerate(zip(bars, top_features.values)):
            ax.text(bar.get_width() + bar.get_width() * 0.01, bar.get_y() + bar.get_height()/2,
                   f'{value:.2e}', ha='left', va='center', fontsize=8)

        plt.tight_layout()
        self.save_figure(fig, 'feature_importance_analysis.png')
        print("特征重要性分析图已保存")

        # 打印重要性排名
        print(f"\n特征重要性排名（前10）:")
        for i, (feature, variance) in enumerate(top_features.head(10).items()):
            print(f"  {i+1:2d}. {feature}: {variance:.4e}")

        return top_features



    def generate_comprehensive_analysis_report(self):
        """生成综合分析报告"""

        # 基本统计
        self.analyze_basic_statistics()

        # 故障类型分析
        self.analyze_fault_type_differences()

        # 相关性分析
        self.analyze_feature_correlations()

        # 生成报告文本
        report_lines = [
            "# 轴承故障诊断数据分析报告",
            "=" * 50,
            "",
            "## 数据概况",
            f"- 总样本数: {len(self.features_data)}",
            f"- 特征维度: {len([col for col in pd.DataFrame(self.features_data).columns if col not in ['file_path', 'fault_type', 'sensor_type', 'rpm', 'original_fs', 'resampled_fs']])}",
            "",
            "## 故障类型分布",
        ]

        # 添加故障类型统计
        if self.features_data:
            df = pd.DataFrame(self.features_data)
            fault_counts = df['fault_type'].value_counts()
            for fault_type, count in fault_counts.items():
                percentage = count / len(self.features_data) * 100
                report_lines.append(f"- {fault_type}: {count} ({percentage:.1f}%)")

        report_lines.extend([
            "",
            "## 采样率分布",
        ])

        # 添加采样率统计
        if self.features_data:
            df = pd.DataFrame(self.features_data)
            if 'original_fs' in df.columns:
                fs_counts = df['original_fs'].value_counts()
                for fs, count in fs_counts.items():
                    percentage = count / len(self.features_data) * 100
                    report_lines.append(f"- {fs} Hz: {count} ({percentage:.1f}%)")

        # 打印报告内容
        report_text = "\n".join(report_lines)
        print(f"综合分析报告:")
        print(report_text)

        return report_text


def main():
    """主函数"""
    print("=" * 80)
    print("任务1: 数据分析及可视化（基于源域数据集处理结果）")
    print("=" * 80)

    # 使用处理后的数据路径
    processed_data_path = "processed_origin_data_result"

    analyzer = BearingDataAnalyzer(processed_data_path)

    # 步骤1: 加载处理后的数据
    print("步骤1: 加载处理后的数据...")
    analyzer.load_processed_data()

    # 步骤2: 数据分析和可视化
    print("\n步骤2: 数据分析和可视化...")

    # 2.1 基本统计分析
    analyzer.analyze_basic_statistics()

    # 2.2 特征分布可视化
    analyzer.visualize_feature_distributions()

    # 2.3 故障类型差异分析
    analyzer.analyze_fault_type_differences()

    # 2.4 采样率影响分析
    analyzer.analyze_sampling_rate_effects()

    # 2.5 特征相关性分析
    analyzer.analyze_feature_correlations()

    # 2.6 信号示例可视化
    analyzer.visualize_signal_examples()

    # 2.7 特征重要性分析
    analyzer.visualize_feature_importance_analysis()

    # 步骤3: 生成综合分析报告
    print("\n步骤3: 生成综合分析报告...")
    analyzer.generate_comprehensive_analysis_report()

    print(f"\n任务1完成！所有分析结果已在控制台展示")


if __name__ == "__main__":
    main()
